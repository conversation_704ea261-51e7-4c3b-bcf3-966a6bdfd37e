<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <!-- Meta tags untuk encoding dan responsive design -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo $__env->yieldContent('title'); ?> </title>

    <!-- Font Google untuk tampilan yang lebih menarik -->
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap CSS untuk framework styling -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- CSS kustom aplikasi -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css']); ?>

    <!-- Font Awesome untuk ikon-ikon -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <style>
        /* ===== STYLING NAVBAR ===== */

        /* Navbar utama dengan efek modern */
        .navbar {
            position: fixed; /* Navbar tetap di atas saat scroll */
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030; /* Layer tinggi agar selalu di atas */
            /* Background gradient abu-abu gelap dengan transparansi */
            background: linear-gradient(135deg, rgba(71, 71, 68, 0.97), rgba(52, 52, 49, 0.97)) !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); /* Bayangan untuk depth */
            transition: all 0.3s ease; /* Animasi smooth untuk semua perubahan */
            backdrop-filter: blur(10px); /* Efek blur glass morphism */
            border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* Border halus */
        }

        /* Efek overlay emas saat hover pada navbar */
        .navbar::before {
            content: ''; /* Pseudo element kosong */
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            /* Gradient emas horizontal yang halus */
            background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
            opacity: 0; /* Tersembunyi secara default */
            transition: opacity 0.3s ease; /* Animasi fade in/out */
        }

        /* Tampilkan overlay emas saat navbar di-hover */
        .navbar:hover::before {
            opacity: 1;
        }

        /* ===== STYLING BRAND/LOGO ===== */

        /* Brand/logo sekolah di navbar */
        .navbar-brand {
            font-weight: 600; /* Font tebal untuk emphasis */
            color: #ffffff !important; /* Warna putih untuk kontras */
            font-size: 1.5rem; /* Ukuran font lebih besar */
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3); /* Bayangan teks untuk depth */
        }

        /* Efek hover pada brand */
        .navbar-brand:hover {
            color: #f8f9fa !important; /* Warna abu-abu terang saat hover */
        }

        /* ===== STYLING NAVIGATION LINKS ===== */

        /* Link navigasi utama */
        .nav-link {
            color: #ffffff !important; /* Warna putih untuk kontras dengan background gelap */
            font-weight: 500; /* Font semi-bold */
            padding: 0.5rem 1rem !important; /* Padding untuk area klik yang nyaman */
            transition: all 0.3s ease; /* Animasi smooth untuk semua perubahan */
            position: relative; /* Untuk positioning pseudo elements */
        }

        /* Efek hover pada navigation links */
        .nav-link:hover {
            color: #ffd700 !important; /* Warna emas saat hover */
            transform: translateY(-1px); /* Efek naik sedikit saat hover */
        }

        /* Efek focus pada navigation links (untuk accessibility) */
        .nav-link:focus {
            color: #ffd700 !important; /* Warna emas saat focus */
        }

        /* Styling untuk link yang sedang aktif */
        .nav-link.active {
            color: #ffd700 !important; /* Warna emas untuk halaman aktif */
            font-weight: 600; /* Font lebih tebal */
        }

        /* Styling panah dropdown */
        .dropdown-toggle::after {
            color: #ffffff; /* Warna putih untuk panah dropdown */
        }

        /* ===== STYLING DROPDOWN MENU ===== */

        /* Container dropdown menu */
        .dropdown-menu {
            border: none; /* Hilangkan border default */
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); /* Bayangan untuk depth */
            border-radius: 8px; /* Sudut melengkung */
            background-color: #ffffff; /* Background putih */
            margin-top: 0.5rem; /* Jarak dari trigger */
        }

        /* Item-item dalam dropdown */
        .dropdown-item {
            padding: 0.7rem 1.5rem; /* Padding yang nyaman */
            transition: all 0.3s ease; /* Animasi smooth */
            color: #2c3e50; /* Warna teks gelap */
            font-weight: 500; /* Font semi-bold */
        }

        /* Efek hover pada dropdown items */
        .dropdown-item:hover {
            background-color: #f8f9fa; /* Background abu-abu terang */
            color: #ffd700; /* Warna teks emas */
            transform: translateX(5px); /* Efek geser ke kanan */
        }

        /* Efek focus pada dropdown items (untuk accessibility) */
        .dropdown-item:focus {
            background-color: #f8f9fa; /* Background abu-abu terang */
            color: #ffd700; /* Warna teks emas */
        }

        /* ===== LAYOUT ADJUSTMENTS ===== */

        /* Sesuaikan konten utama agar tidak tertutup navbar fixed */
        main {
            padding-top: 76px; /* Sesuaikan dengan tinggi navbar */
        }

        /* ===== STYLING MOBILE MENU TOGGLER ===== */

        /* Tombol hamburger untuk mobile */
        .navbar-toggler {
            border: 2px solid #ffffff; /* Border putih */
            padding: 0.25rem 0.5rem; /* Padding yang nyaman */
        }

        /* Efek focus pada tombol hamburger */
        .navbar-toggler:focus {
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25); /* Ring putih saat focus */
        }

        /* Ikon hamburger dengan warna putih */
        .navbar-toggler-icon {
            /* SVG ikon hamburger dengan stroke putih */
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        /* ===== RESPONSIVE DESIGN ===== */

        /* Styling untuk layar mobile dan tablet */
        @media (max-width: 991.98px) {
            /* Container menu mobile yang collapse */
            .navbar-collapse {
                background: rgba(71, 71, 68, 0.98); /* Background konsisten dengan navbar */
                padding: 1rem; /* Padding yang nyaman */
                border-radius: 8px; /* Sudut melengkung */
                margin-top: 0.5rem; /* Jarak dari navbar */
                border: 1px solid rgba(255, 255, 255, 0.1); /* Border halus */
            }

            /* Link navigasi di mobile */
            .nav-link {
                padding: 0.75rem 1rem !important; /* Padding lebih besar untuk touch */
                border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* Pemisah antar link */
            }

            /* Hilangkan border pada link terakhir */
            .nav-link:last-child {
                border-bottom: none;
            }
        }
    </style>
</head>
<body>
    <!-- ===== NAVIGATION BAR ===== -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <!-- Brand/Logo Sekolah -->
            <a class="navbar-brand" href="/">
                <img src="<?php echo e(asset('vendor/adminlte/dist/img/AdminLTELogo.png')); ?>" alt="Logo" height="40" class="me-2 align-middle">
            </a>
            <a class="navbar-brand" href="/">Sekolah Pelopor1</a>
            <!-- Tombol hamburger untuk mobile -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Menu navigasi yang dapat di-collapse -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto"> <!-- ms-auto untuk align ke kanan -->
                    <!-- Menu Beranda -->
                    <li class="nav-item">
                        <a class="nav-link" href="/">Beranda</a>
                    </li>

                    <!-- Menu Dropdown Sekolah -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Sekolah
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo e(route('website.jenjang.profil', 'paud')); ?>">PAUD</a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('website.jenjang.profil', 'sd')); ?>">SD</a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('website.jenjang.profil', 'smp')); ?>">SMP</a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('website.jenjang.profil', 'sma')); ?>">SMA</a></li>
                        </ul>
                    </li>

                    <!-- Menu Artikel -->
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('website.artikel')); ?>">Artikel</a>
                    </li>

                    <!-- Menu Event -->
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('website.event')); ?>">Event</a>
                    </li>

                    <!-- Menu Dropdown Prestasi -->
                    <!-- <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Prestasi
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo e(route('website.prestasi.jenjang', ['jenjang' => 'paud'])); ?>">PAUD</a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('website.prestasi.jenjang', ['jenjang' => 'sd'])); ?>">SD</a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('website.prestasi.jenjang', ['jenjang' => 'smp'])); ?>">SMP</a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('website.prestasi.jenjang', ['jenjang' => 'sma'])); ?>">SMA</a></li>
                            <li><hr class="dropdown-divider"></li>  
                            <li><a class="dropdown-item" href="<?php echo e(route('website.prestasi.all')); ?>">Semua Prestasi</a></li>
                        </ul>
                    </li> --> 
                

                    <!-- Menu Prestasi -->
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('website.prestasi.all')); ?>">Prestasi</a>
                    </li>

                    <!-- Menu Fasilitas -->
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('website.fasilitas')); ?>">Fasilitas</a>
                    </li>

                    <!-- Menu Ekstrakurikuler -->
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('website.ekstrakurikuler')); ?>">Ekstrakurikuler</a>
                    </li>
                    <!-- Menu Dropdown Siswa -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Siswa
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href=" ">Cek SPP</a></li>
                            <li><a class="dropdown-item" href=" ">Jadwal</a></li>
                            <!-- Menu siswa lainnya bisa ditambahkan di sini -->
                            <!-- <li><a class="dropdown-item" href="<?php echo e(route('website.prestasi.jenjang', ['jenjang' => 'smp'])); ?>">SMP</a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('website.prestasi.jenjang', ['jenjang' => 'sma'])); ?>">SMA</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('website.prestasi.all')); ?>">Semua Prestasi</a></li> -->
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- ===== KONTEN UTAMA ===== -->
    <main>
        <?php echo $__env->yieldContent('content'); ?> <!-- Konten dari halaman child akan ditampilkan di sini -->
    </main>

    <!-- ===== FOOTER SECTION ===== -->
    <footer class="py-3 mt-4 text-light" style="background-color: rgb(6, 4, 63);">
        <div class="container">
            <!-- Konten utama footer -->
            <div class="row">
                <!-- Informasi kontak sekolah -->
                <div class="col-md-2">
                    <h5 class="h6">SEKOLAH PELOPOR</h5>
                    <p class="small mb-2">
                        Jl. Kampung Lalang<br>
                        Telepon: (0762) 563-2262<br>
                        Email: <EMAIL>
                    </p>
                </div>

                <!-- Deskripsi sekolah -->
                <div class="col-md-4">
                    <h5 class="h6 text-center mb-2">SEKOLAH PELOPOR</h5>
                    <p class="small text-justify" style="text-align: justify;">
                        Sekolah Pelopor adalah salah satu sekolah swasta di Duri yang berada dibawah naungan Yayasan Pubbarama Duri. Sekolah pelopor terdiri dari tiga unit, Yaitu PAUD Pelopor, SDS Pelopor Duri, SMPS Pelopor Mandau.
                    </p>
                </div>

                <!-- Link navigasi cepat -->
                <div class="col-md-3">
                    <h5 class="h6">Link Cepat</h5>
                    <ul class="list-unstyled small mb-2">
                        <li><a href="<?php echo e(route('website.halaman.profil')); ?>" class="text-light">Profil</a></li>
                        <li><a href="<?php echo e(route('website.artikel')); ?>" class="text-light">Artikel</a></li>
                        <li><a href="<?php echo e(route('website.prestasi')); ?>" class="text-light">Prestasi</a></li>
                        <li><a href="<?php echo e(route('website.ekstrakurikuler')); ?>" class="text-light">Ekstrakurikuler</a></li>
                    </ul>
                </div>

                <!-- Link media sosial -->
                <div class="col-md-3">
                    <h5 class="h6">Media Sosial</h5>
                    <div class="social-links">
                        <!-- Link Facebook sekolah -->
                        <a href="https://www.facebook.com/share/194eihzDY5/" class="text-light me-2" target="_blank"><i class="fab fa-facebook"></i></a>
                        <!-- Link YouTube sekolah -->
                        <a href="https://www.youtube.com/@sugiyonogiyon7235" target="_blank" class="text-light"><i class="fab fa-youtube"></i></a>
                        <!-- Media sosial lainnya bisa ditambahkan di sini -->
                        <!--<a href="#" class="text-light me-2"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-2"><i class="fab fa-instagram"></i></a> -->
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- ===== COPYRIGHT SECTION ===== -->
    <div class="py-2" style="background-color: rgb(9, 1, 22);">
        <div class="container">
            <p class="text-center text-light small mb-0">Copyright ©2024 SEKOLAH PELOPOR DURI. All Rights Reserved</p>
        </div>
    </div>

    <!-- ===== JAVASCRIPT LIBRARIES ===== -->
    <!-- Bootstrap JS Bundle dengan Popper untuk komponen interaktif -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome Kit (jika diperlukan) -->
    <script src="https://kit.fontawesome.com/your-code.js"></script>
    <!-- JavaScript kustom aplikasi -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/app.js']); ?>
</body>
</html>


<?php /**PATH C:\xampp\htdocs\webplp\resources\views/layouts/website.blade.php ENDPATH**/ ?>